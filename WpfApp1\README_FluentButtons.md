# Fluent Design System 按钮样式

这个项目包含了符合Microsoft Fluent Design System设计规范的WPF按钮样式。

## 功能特性

### 🎨 设计特征
- **圆角边框**：4px圆角半径，符合现代设计趋势
- **平滑动画**：悬停和按下状态的流畅过渡效果
- **阴影效果**：悬停时的微妙阴影提升视觉层次
- **现代配色**：使用Microsoft官方色彩规范

### 🔧 技术实现
- **可复用样式**：定义在ResourceDictionary中，全局可用
- **状态管理**：使用VisualStateManager管理不同交互状态
- **自定义模板**：完全自定义的ControlTemplate
- **无障碍支持**：确保足够的颜色对比度

## 可用样式

### 1. FluentPrimaryButtonStyle（主按钮）
- **用途**：最重要的操作（保存、确认、提交等）
- **颜色**：蓝色背景 (#0078D4)
- **文字**：白色

### 2. FluentSecondaryButtonStyle（次要按钮）
- **用途**：次要操作（取消、重置等）
- **颜色**：透明背景，蓝色边框和文字
- **继承**：基于主按钮样式

### 3. FluentAccentButtonStyle（强调按钮）
- **用途**：特殊或高级功能
- **颜色**：紫色背景 (#8764B8)
- **文字**：白色

## 使用方法

### 基本用法

```xml
<!-- 主按钮 -->
<Button Content="保存" Style="{StaticResource FluentPrimaryButtonStyle}"/>

<!-- 次要按钮 -->
<Button Content="取消" Style="{StaticResource FluentSecondaryButtonStyle}"/>

<!-- 强调按钮 -->
<Button Content="高级设置" Style="{StaticResource FluentAccentButtonStyle}"/>
```

### 自定义属性

```xml
<Button Content="自定义按钮" 
        Style="{StaticResource FluentPrimaryButtonStyle}"
        MinWidth="120"
        MinHeight="40"
        Margin="10"
        FontSize="16"/>
```

## 状态说明

### 交互状态
- **Normal**：默认状态
- **MouseOver**：鼠标悬停，颜色变深，显示阴影
- **Pressed**：按下状态，轻微缩放效果
- **Disabled**：禁用状态，灰色显示

### 动画效果
- **颜色过渡**：0.2秒平滑颜色变化
- **阴影动画**：悬停时阴影淡入淡出
- **缩放效果**：按下时轻微缩放（98%）

## 文件结构

```
WpfApp1/
├── Styles/
│   └── FluentButtonStyles.xaml    # 按钮样式定义
├── App.xaml                       # 引用样式资源
├── MainWindow.xaml                # 演示页面
└── README_FluentButtons.md        # 使用说明
```

## 颜色规范

### 主按钮颜色
- **Normal**: #0078D4 (Microsoft Blue)
- **Hover**: #106EBE (深蓝色)
- **Pressed**: #005A9E (更深蓝色)
- **Disabled**: #F3F2F1 (浅灰色)

### 强调按钮颜色
- **Normal**: #8764B8 (紫色)
- **文字颜色**: White
- **禁用文字**: #A19F9D

## 最佳实践

1. **按钮层次**：在同一界面中，主按钮不超过1个
2. **文字长度**：保持按钮文字简洁明了
3. **间距设置**：按钮之间保持适当间距（推荐10-16px）
4. **尺寸一致**：同类按钮保持相同尺寸
5. **状态反馈**：确保用户能清楚感知按钮状态变化

## 扩展说明

如需添加新的按钮样式，可以基于现有样式进行扩展：

```xml
<Style x:Key="CustomButtonStyle" TargetType="Button" BasedOn="{StaticResource FluentPrimaryButtonStyle}">
    <Setter Property="Background" Value="YourColor"/>
    <Setter Property="BorderBrush" Value="YourBorderColor"/>
</Style>
```

这样可以继承所有动画和交互效果，只需修改颜色等特定属性。

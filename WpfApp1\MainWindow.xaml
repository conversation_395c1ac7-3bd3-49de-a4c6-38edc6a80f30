<Window x:Class="WpfApp1.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfApp1"
        mc:Ignorable="d"
        Title="Fluent Design Button Demo" Height="600" Width="900"
        Background="#F3F2F1">
    <Grid Margin="40">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0"
                   Text="Fluent Design System 按钮样式演示"
                   FontSize="28"
                   FontWeight="Bold"
                   Foreground="#323130"
                   Margin="0,0,0,30"
                   HorizontalAlignment="Center"/>

        <!-- 主按钮演示 -->
        <StackPanel Grid.Row="1" Orientation="Vertical" Margin="0,0,0,30">
            <TextBlock Text="主按钮 (Primary Button)"
                       FontSize="18"
                       FontWeight="SemiBold"
                       Foreground="#323130"
                       Margin="0,0,0,15"/>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="保存"
                        Style="{StaticResource FluentPrimaryButtonStyle}"
                        Margin="10"/>
                <Button Content="确认"
                        Style="{StaticResource FluentPrimaryButtonStyle}"
                        Margin="10"/>
                <Button Content="禁用状态"
                        Style="{StaticResource FluentPrimaryButtonStyle}"
                        IsEnabled="False"
                        Margin="10"/>
            </StackPanel>
        </StackPanel>

        <!-- 次要按钮演示 -->
        <StackPanel Grid.Row="2" Orientation="Vertical" Margin="0,0,0,30">
            <TextBlock Text="次要按钮 (Secondary Button)"
                       FontSize="18"
                       FontWeight="SemiBold"
                       Foreground="#323130"
                       Margin="0,0,0,15"/>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="取消"
                        Style="{StaticResource FluentSecondaryButtonStyle}"
                        Margin="10"/>
                <Button Content="重置"
                        Style="{StaticResource FluentSecondaryButtonStyle}"
                        Margin="10"/>
                <Button Content="禁用状态"
                        Style="{StaticResource FluentSecondaryButtonStyle}"
                        IsEnabled="False"
                        Margin="10"/>
            </StackPanel>
        </StackPanel>

        <!-- 强调按钮演示 -->
        <StackPanel Grid.Row="3" Orientation="Vertical" Margin="0,0,0,30">
            <TextBlock Text="强调按钮 (Accent Button)"
                       FontSize="18"
                       FontWeight="SemiBold"
                       Foreground="#323130"
                       Margin="0,0,0,15"/>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="特殊操作"
                        Style="{StaticResource FluentAccentButtonStyle}"
                        Margin="10"/>
                <Button Content="高级功能"
                        Style="{StaticResource FluentAccentButtonStyle}"
                        Margin="10"/>
                <Button Content="禁用状态"
                        Style="{StaticResource FluentAccentButtonStyle}"
                        IsEnabled="False"
                        Margin="10"/>
            </StackPanel>
        </StackPanel>

        <!-- 使用说明 -->
        <Border Grid.Row="4"
                Background="White"
                BorderBrush="#E1DFDD"
                BorderThickness="1"
                CornerRadius="4"
                Padding="20"
                Margin="0,20,0,0">
            <StackPanel>
                <TextBlock Text="使用说明"
                           FontSize="16"
                           FontWeight="SemiBold"
                           Foreground="#323130"
                           Margin="0,0,0,10"/>
                <TextBlock TextWrapping="Wrap"
                           Foreground="#605E5C"
                           LineHeight="20">
                    <Run Text="• 主按钮：用于最重要的操作，如保存、确认等"/>
                    <LineBreak/>
                    <Run Text="• 次要按钮：用于次要操作，如取消、重置等"/>
                    <LineBreak/>
                    <Run Text="• 强调按钮：用于特殊或高级功能"/>
                    <LineBreak/>
                    <Run Text="• 所有按钮都支持悬停、按下和禁用状态的平滑动画效果"/>
                    <LineBreak/>
                    <Run Text="• 按钮样式遵循Microsoft Fluent Design System设计规范"/>
                </TextBlock>
            </StackPanel>
        </Border>
    </Grid>
</Window>

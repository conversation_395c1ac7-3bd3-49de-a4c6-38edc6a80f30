{"Version": 1, "WorkspaceRootPath": "D:\\新建文件夹\\WpfApp1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{EFC0B5DA-947E-40FE-A238-B4306F0190FA}|WpfApp1\\WpfApp1.csproj|d:\\新建文件夹\\wpfapp1\\wpfapp1\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{EFC0B5DA-947E-40FE-A238-B4306F0190FA}|WpfApp1\\WpfApp1.csproj|solutionrelative:wpfapp1\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{EFC0B5DA-947E-40FE-A238-B4306F0190FA}|WpfApp1\\WpfApp1.csproj|d:\\新建文件夹\\wpfapp1\\wpfapp1\\styles\\fluentbuttonstyles.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{EFC0B5DA-947E-40FE-A238-B4306F0190FA}|WpfApp1\\WpfApp1.csproj|solutionrelative:wpfapp1\\styles\\fluentbuttonstyles.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{EFC0B5DA-947E-40FE-A238-B4306F0190FA}|WpfApp1\\WpfApp1.csproj|d:\\新建文件夹\\wpfapp1\\wpfapp1\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{EFC0B5DA-947E-40FE-A238-B4306F0190FA}|WpfApp1\\WpfApp1.csproj|solutionrelative:wpfapp1\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{EFC0B5DA-947E-40FE-A238-B4306F0190FA}|WpfApp1\\WpfApp1.csproj|d:\\新建文件夹\\wpfapp1\\wpfapp1\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EFC0B5DA-947E-40FE-A238-B4306F0190FA}|WpfApp1\\WpfApp1.csproj|solutionrelative:wpfapp1\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EFC0B5DA-947E-40FE-A238-B4306F0190FA}|WpfApp1\\WpfApp1.csproj|d:\\新建文件夹\\wpfapp1\\wpfapp1\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EFC0B5DA-947E-40FE-A238-B4306F0190FA}|WpfApp1\\WpfApp1.csproj|solutionrelative:wpfapp1\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 11, "Children": [{"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "FluentButtonStyles.xaml", "DocumentMoniker": "D:\\新建文件夹\\WpfApp1\\WpfApp1\\Styles\\FluentButtonStyles.xaml", "RelativeDocumentMoniker": "WpfApp1\\Styles\\FluentButtonStyles.xaml", "ToolTip": "D:\\新建文件夹\\WpfApp1\\WpfApp1\\Styles\\FluentButtonStyles.xaml", "RelativeToolTip": "WpfApp1\\Styles\\FluentButtonStyles.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T07:16:17.98Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "App.xaml", "DocumentMoniker": "D:\\新建文件夹\\WpfApp1\\WpfApp1\\App.xaml", "RelativeDocumentMoniker": "WpfApp1\\App.xaml", "ToolTip": "D:\\新建文件夹\\WpfApp1\\WpfApp1\\App.xaml", "RelativeToolTip": "WpfApp1\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T07:15:41.52Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "App.xaml.cs", "DocumentMoniker": "D:\\新建文件夹\\WpfApp1\\WpfApp1\\App.xaml.cs", "RelativeDocumentMoniker": "WpfApp1\\App.xaml.cs", "ToolTip": "D:\\新建文件夹\\WpfApp1\\WpfApp1\\App.xaml.cs", "RelativeToolTip": "WpfApp1\\App.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T07:15:38.986Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\新建文件夹\\WpfApp1\\WpfApp1\\MainWindow.xaml", "RelativeDocumentMoniker": "WpfApp1\\MainWindow.xaml", "ToolTip": "D:\\新建文件夹\\WpfApp1\\WpfApp1\\MainWindow.xaml", "RelativeToolTip": "WpfApp1\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T07:13:21.828Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "D:\\新建文件夹\\WpfApp1\\WpfApp1\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "WpfApp1\\MainWindow.xaml.cs", "ToolTip": "D:\\新建文件夹\\WpfApp1\\WpfApp1\\MainWindow.xaml.cs", "RelativeToolTip": "WpfApp1\\MainWindow.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T07:13:19.887Z", "EditorCaption": ""}]}]}]}
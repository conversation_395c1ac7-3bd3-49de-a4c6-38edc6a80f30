<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Fluent Design Primary Button Style -->
    <Style x:Key="FluentPrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="#0078D4"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderBrush" Value="#0078D4"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="ButtonBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect x:Name="ButtonShadow"
                                              Color="Black"
                                              Opacity="0"
                                              BlurRadius="8"
                                              ShadowDepth="2"
                                              Direction="270"/>
                        </Border.Effect>
                        <ContentPresenter x:Name="ContentPresenter"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}"
                                          Focusable="False"/>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal">
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                        To="#0078D4"
                                                        Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetName="ButtonShadow"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0"
                                                         Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetName="ButtonBorder"
                                                         Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                         To="1"
                                                         Duration="0:0:0.1"/>
                                        <DoubleAnimation Storyboard.TargetName="ButtonBorder"
                                                         Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                         To="1"
                                                         Duration="0:0:0.1"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                        To="#106EBE"
                                                        Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetName="ButtonShadow"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.2"
                                                         Duration="0:0:0.2"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                        To="#005A9E"
                                                        Duration="0:0:0.1"/>
                                        <DoubleAnimation Storyboard.TargetName="ButtonShadow"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.1"
                                                         Duration="0:0:0.1"/>
                                        <DoubleAnimation Storyboard.TargetName="ButtonBorder"
                                                         Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                         To="0.98"
                                                         Duration="0:0:0.1"/>
                                        <DoubleAnimation Storyboard.TargetName="ButtonBorder"
                                                         Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                         To="0.98"
                                                         Duration="0:0:0.1"/>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetName="ButtonBorder"
                                                        Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                        To="#F3F2F1"
                                                        Duration="0:0:0.2"/>
                                        <ColorAnimation Storyboard.TargetName="ContentPresenter"
                                                        Storyboard.TargetProperty="(TextElement.Foreground).(SolidColorBrush.Color)"
                                                        To="#A19F9D"
                                                        Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetName="ButtonShadow"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0"
                                                         Duration="0:0:0.2"/>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="#F3F2F1"/>
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="#E1DFDD"/>
                            <Setter TargetName="ContentPresenter" Property="TextElement.Foreground" Value="#A19F9D"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
    </Style>

    <!-- Fluent Design Secondary Button Style -->
    <Style x:Key="FluentSecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource FluentPrimaryButtonStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="#0078D4"/>
        <Setter Property="BorderBrush" Value="#0078D4"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- Fluent Design Accent Button Style -->
    <Style x:Key="FluentAccentButtonStyle" TargetType="Button" BasedOn="{StaticResource FluentPrimaryButtonStyle}">
        <Setter Property="Background" Value="#8764B8"/>
        <Setter Property="BorderBrush" Value="#8764B8"/>
    </Style>

</ResourceDictionary>
